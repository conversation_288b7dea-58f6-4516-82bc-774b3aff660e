import type {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  GetMessagesBody,
  JoinConversationBody,
  ReadMessagesBody,
  SendMessagesBody,
  SocketEventParams,
} from "~/socket";

import { events } from "~/lib/events";
import { prisma } from "~/lib/prisma";

async function getConversations(
  { io, socket }: SocketEventParams,
  ack?: AckCallback,
) {
  const userAuth = socket.request.user;

  try {
    console.log("Get Conversations for: ", { user: userAuth });

    const conversations = await prisma.conversation.findMany({
      where: {
        members: {
          some: {
            id: userAuth.id,
          },
        },
      },
    });

    socket.join(userAuth.id);

    io.in(userAuth.id).emit(events.conversations.receive, conversations);

    console.log("Conversations: ", conversations);

    if (ack) {
      ack({ success: true, data: conversations });
    }
  } catch (error) {
    console.error("Error getting conversations:", error);

    if (ack) {
      ack({ success: false, error: "Failed to get conversations" });
    }
  }
}

async function joinConversations(
  {
    io: _io,
    socket,
    type,
    memberAuthId,
    referenceId,
  }: SocketEventParams & JoinConversationBody,
  ack?: AckCallback,
) {
  const userAuth = socket.request.user;

  try {
    console.log("Join Conversation for: ", {
      user: userAuth,
      type,
      memberAuthId,
      referenceId,
    });

    let conversation = await prisma.conversation.findFirst({
      where: {
        type,
        referenceId,
        AND: [
          { members: { some: { authId: userAuth.id } } },
          { members: { some: { authId: memberAuthId } } },
        ],
      },
    });

    if (!conversation) {
      console.log("Create Conversation for: ", {
        user: userAuth,
        type,
        memberAuthId,
        referenceId,
      });

      conversation = await prisma.conversation.create({
        data: {
          type,
          referenceId,
        },
      });

      await prisma.conversationToAuth.createMany({
        data: [
          {
            conversationId: conversation.id,
            authId: userAuth.id,
          },
          {
            conversationId: conversation.id,
            authId: memberAuthId,
          },
        ],
      });

      // TODO: Send First Message From User (Not Vendor/Logistic)
    }

    socket.join(conversation.id);

    console.log("Conversation: ", conversation);

    if (ack) {
      ack({ success: true, data: conversation });
    }
  } catch (error) {
    console.error("Error joining conversation:", error);

    if (ack) {
      ack({ success: false, error: "Failed to join conversation" });
    }
  }
}

async function getMessages(
  { io, socket, conversationId }: SocketEventParams & GetMessagesBody,
  ack?: AckCallback,
) {
  const userAuth = socket.request.user;

  try {
    console.log("Get Messages for: ", { user: userAuth });

    const messages = await prisma.message.findMany({
      where: {
        conversationId,
      },
    });

    io.in(conversationId).emit(events.messages.receive, messages);

    console.log("Messages: ", messages);

    if (ack) {
      ack({ success: true, data: messages });
    }
  } catch (error) {
    console.error("Error getting messages:", error);

    if (ack) {
      ack({ success: false, error: "Failed to get messages" });
    }
  }
}

async function sendMessages(
  { io, socket, conversationId, content }: SocketEventParams & SendMessagesBody,
  ack?: AckCallback,
) {
  const userAuth = socket.request.user;

  try {
    console.log("Send Messages for: ", { user: userAuth });

    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
    });

    if (!conversation) {
      throw new Error("Conversation Not Found!");
    }

    const message = await prisma.message.create({
      data: {
        content,
        sender: {
          connect: { id: userAuth.id },
        },
        conversation: {
          connect: { id: conversation.id },
        },
      },
    });

    io.in(conversationId).emit(events.messages.receive, [message]);

    console.log("Message: ", message);

    if (ack) {
      ack({ success: true, data: message });
    }
  } catch (error) {
    console.error("Error sending message:", error);

    if (ack) {
      ack({ success: false, error: "Failed to send message" });
    }
  }
}

async function readMessages(
  {
    io: _io,
    socket,
    conversationId,
    messageIds,
  }: SocketEventParams & ReadMessagesBody,
  ack?: AckCallback,
) {
  const userAuth = socket.request.user;

  try {
    console.log("Read Messages for: ", { user: userAuth });

    const messages = await prisma.message.updateMany({
      where: {
        id: { in: messageIds },
        senderId: { not: userAuth.id },
        conversationId,
        isRead: false,
      },
      data: {
        isRead: true,
      },
    });

    console.log("Messages: ", messages);

    if (ack) {
      ack({ success: true, data: { count: messages.count } });
    }
  } catch (error) {
    console.error("Error reading messages:", error);

    if (ack) {
      ack({ success: false, error: "Failed to mark messages as read" });
    }
  }
}

export {
  getConversations,
  joinConversations,
  getMessages,
  sendMessages,
  readMessages,
};
