import type { Server as HttpServer } from "node:http";

import type { ConversationType, Role } from "@prisma/client";
import type { Socket } from "socket.io";

import { Server as SocketServer } from "socket.io";

import {
  getConversations,
  getMessages,
  joinConversations,
  readMessages,
  sendMessages,
} from "~/events/conversations";
import { events } from "~/lib/events";
import { verifyRequest } from "~/middlewares/auth";
import { expandSocketResponse } from "~/middlewares/response";

export type AuthenticatedSocket = Socket & {
  request: {
    user: {
      id: string;
      email: string;
      status: string;
      role: Role;
      isVerified: boolean;
      isDeleted: boolean;
      createdAt: Date;
      updatedAt: Date;
    };
  };
};

export type SocketEventParams = {
  io: SocketServer;
  socket: AuthenticatedSocket;
};

export type AckCallback = (response: {
  success: boolean;
  data?: unknown;
  error?: string;
}) => void;

export type JoinConversationBody = {
  type: ConversationType;
  memberAuthId: string;
  referenceId?: string;
};

export type GetMessagesBody = {
  conversationId: string;
};

export type SendMessagesBody = {
  conversationId: string;
  content: string;
};

export type ReadMessagesBody = {
  conversationId: string;
  messageIds: string[];
};

function setupSocket(server: HttpServer) {
  const io = new SocketServer(server, {
    cors: {
      origin: "*",
    },
  });

  io.engine.use(expandSocketResponse);

  // @ts-ignore
  io.engine.use((request, response, next) => {
    const isHandshake = request._query.sid === undefined;

    if (!isHandshake) {
      return next();
    }

    verifyRequest({
      isVerified: true,
      isDeleted: false,
      allowedTypes: ["ACCESS"],
      allowedStatus: ["APPROVED"],
      allowedRoles: ["SUPER_ADMIN", "ADMIN", "LOGISTIC", "VENDOR", "USER"],
    })(request, response, next);
  });

  io.on(events.socket.connection, (socket: AuthenticatedSocket) => {
    console.log("Connected");

    const userAuth = socket.request.user;

    console.log("User", userAuth);

    socket.on(events.conversations.get, async (ack?: AckCallback) => {
      await getConversations({ io, socket }, ack);
    });

    socket.on(
      events.conversations.join,
      async (body: JoinConversationBody, ack?: AckCallback) => {
        await joinConversations(
          {
            io,
            socket,
            ...body,
          },
          ack,
        );
      },
    );

    socket.on(
      events.messages.get,
      async (body: GetMessagesBody, ack?: AckCallback) => {
        await getMessages({ io, socket, ...body }, ack);
      },
    );

    socket.on(
      events.messages.send,
      async (body: SendMessagesBody, ack?: AckCallback) => {
        await sendMessages({ io, socket, ...body }, ack);
      },
    );

    socket.on(
      events.messages.read,
      async (body: ReadMessagesBody, ack?: AckCallback) => {
        await readMessages({ io, socket, ...body }, ack);
      },
    );

    socket.on(events.socket.disconnect, () => {
      console.log("Disconnected");
    });
  });
}

export { setupSocket };
