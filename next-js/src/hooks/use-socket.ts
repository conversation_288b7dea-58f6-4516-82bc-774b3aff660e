import { useCallback, useEffect, useState } from "react";

import type { Socket } from "socket.io-client";

import { socketClient } from "~/lib/socket";

export const useSocket = (token: string | null) => {
  const [isConnected, setIsConnected] = useState(false);

  const connect = useCallback(() => {
    if (!token) return;

    const socket = socketClient.connect(token);
    if (socket) {
      socket.on("connect", () => setIsConnected(true));
      socket.on("disconnect", () => setIsConnected(false));
    }
    return socket;
  }, [token]);

  const disconnect = useCallback(() => {
    socketClient.disconnect();
    setIsConnected(false);
  }, []);

  const getSocket = useCallback((): Socket | null => {
    return socketClient.getSocket();
  }, []);

  useEffect(() => {
    if (token) {
      connect();
    }

    return () => {
      // Don't disconnect on unmount as other components might be using the socket
      // disconnect();
    };
  }, [token, connect]);

  return {
    socket: getSocket(),
    isConnected,
    connect,
    disconnect,
  };
};
