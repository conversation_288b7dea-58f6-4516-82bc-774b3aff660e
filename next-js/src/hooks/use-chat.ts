import { useCallback, useEffect, useState } from "react";

import type { Socket } from "socket.io-client";

import { events } from "~/lib/events";
import type {
  ConversationType,
  PublicConversation,
  PublicMessage,
} from "~/lib/types";
import { useSocket } from "./use-socket";

export type JoinConversationParams = {
  type: ConversationType;
  memberAuthId: string;
  referenceId?: string;
};

export type SendMessageParams = {
  conversationId: string;
  content: string;
};

export type ReadMessagesParams = {
  conversationId: string;
  messageIds: string[];
};

export const useChat = (token: string | null) => {
  const { socket, isConnected } = useSocket(token);
  const [conversations, setConversations] = useState<PublicConversation[]>([]);
  const [messages, setMessages] = useState<Record<string, PublicMessage[]>>({});
  const [activeConversationId, setActiveConversationId] = useState<
    string | null
  >(null);
  const [isLoading, setIsLoading] = useState(false);

  // Setup socket event listeners
  useEffect(() => {
    if (!socket || !isConnected) return;

    const handleReceiveConversations = (
      receivedConversations: PublicConversation[],
    ) => {
      setConversations(receivedConversations);
    };

    const handleReceiveMessages = (receivedMessages: PublicMessage[]) => {
      if (receivedMessages.length > 0) {
        const conversationId = receivedMessages[0].conversationId;
        setMessages((prev) => ({
          ...prev,
          [conversationId]: [
            ...(prev[conversationId] || []),
            ...receivedMessages,
          ],
        }));
      }
    };

    socket.on(events.conversations.receive, handleReceiveConversations);
    socket.on(events.messages.receive, handleReceiveMessages);

    return () => {
      socket.off(events.conversations.receive, handleReceiveConversations);
      socket.off(events.messages.receive, handleReceiveMessages);
    };
  }, [socket, isConnected]);

  // Get conversations
  const getConversations = useCallback(() => {
    if (!socket || !isConnected) return;

    setIsLoading(true);
    socket.emit(events.conversations.get, (response: any) => {
      setIsLoading(false);
      if (response?.success) {
        setConversations(response.data || []);
      } else {
        console.error("Failed to get conversations:", response?.error);
      }
    });
  }, [socket, isConnected]);

  // Join conversation
  const joinConversation = useCallback(
    (params: JoinConversationParams) => {
      if (!socket || !isConnected) return Promise.reject("Socket not connected");

      return new Promise<PublicConversation>((resolve, reject) => {
        socket.emit(events.conversations.join, params, (response: any) => {
          if (response?.success) {
            setActiveConversationId(response.data.id);
            resolve(response.data);
          } else {
            reject(new Error(response?.error || "Failed to join conversation"));
          }
        });
      });
    },
    [socket, isConnected],
  );

  // Get messages for a conversation
  const getMessages = useCallback(
    (conversationId: string) => {
      if (!socket || !isConnected) return;

      socket.emit(
        events.messages.get,
        { conversationId },
        (response: any) => {
          if (response?.success) {
            setMessages((prev) => ({
              ...prev,
              [conversationId]: response.data || [],
            }));
          } else {
            console.error("Failed to get messages:", response?.error);
          }
        },
      );
    },
    [socket, isConnected],
  );

  // Send message
  const sendMessage = useCallback(
    (params: SendMessageParams) => {
      if (!socket || !isConnected) return Promise.reject("Socket not connected");

      return new Promise<PublicMessage>((resolve, reject) => {
        socket.emit(events.messages.send, params, (response: any) => {
          if (response?.success) {
            resolve(response.data);
          } else {
            reject(new Error(response?.error || "Failed to send message"));
          }
        });
      });
    },
    [socket, isConnected],
  );

  // Read messages
  const readMessages = useCallback(
    (params: ReadMessagesParams) => {
      if (!socket || !isConnected) return;

      socket.emit(events.messages.read, params, (response: any) => {
        if (!response?.success) {
          console.error("Failed to mark messages as read:", response?.error);
        }
      });
    },
    [socket, isConnected],
  );

  // Get messages for active conversation
  const activeMessages = activeConversationId
    ? messages[activeConversationId] || []
    : [];

  // Get active conversation
  const activeConversation = conversations.find(
    (conv) => conv.id === activeConversationId,
  );

  return {
    // State
    conversations,
    messages,
    activeConversationId,
    activeMessages,
    activeConversation,
    isConnected,
    isLoading,

    // Actions
    getConversations,
    joinConversation,
    getMessages,
    sendMessage,
    readMessages,
    setActiveConversationId,
  };
};
