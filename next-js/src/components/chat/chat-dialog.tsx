"use client";

import { useEffect, useState } from "react";

import { MessageCircleIcon, XIcon } from "lucide-react";

import { But<PERSON> } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { useAuthContext } from "~/context/auth";
import { useChat } from "~/hooks/use-chat";
import type { ConversationType } from "~/lib/types";
import { cn } from "~/lib/utils";
import { ChatWindow } from "./chat-window";

interface ChatDialogProps {
  type: ConversationType;
  memberAuthId: string;
  memberName: string;
  memberPictureId: string;
  referenceId?: string;
  triggerText?: string;
  triggerVariant?: "default" | "outline" | "secondary" | "ghost" | "link";
  triggerSize?: "default" | "sm" | "lg" | "icon";
  className?: string;
  children?: React.ReactNode;
}

export function ChatDialog({
  type,
  memberAuthId,
  memberName,
  memberPictureId,
  referenceId,
  triggerText = "Chat",
  triggerVariant = "outline",
  triggerSize = "sm",
  className,
  children,
}: ChatDialogProps) {
  const { token, auth } = useAuthContext();
  const [isOpen, setIsOpen] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);

  const {
    joinConversation,
    getMessages,
    sendMessage,
    readMessages,
    activeMessages,
    isConnected,
  } = useChat(token);

  // Join conversation when dialog opens
  useEffect(() => {
    if (isOpen && !conversationId && isConnected) {
      joinConversation({
        type,
        memberAuthId,
        referenceId,
      })
        .then((conversation) => {
          setConversationId(conversation.id);
          getMessages(conversation.id);
        })
        .catch((error) => {
          console.error("Failed to join conversation:", error);
        });
    }
  }, [
    isOpen,
    conversationId,
    isConnected,
    joinConversation,
    type,
    memberAuthId,
    referenceId,
    getMessages,
  ]);

  const handleSendMessage = async (content: string) => {
    if (!conversationId) return;

    try {
      await sendMessage({ conversationId, content });
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const handleMarkAsRead = (messageIds: string[]) => {
    if (!conversationId) return;

    readMessages({ conversationId, messageIds });
  };

  const currentUserId = auth?.id || "";

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant={triggerVariant}
          size={triggerSize}
          className={cn("gap-2", className)}
        >
          {children || (
            <>
              <MessageCircleIcon className="size-4" />
              {triggerText}
            </>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl h-[600px] p-0">
        <DialogHeader className="p-4 border-b">
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <MessageCircleIcon className="size-5" />
              Chat with {memberName}
            </DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsOpen(false)}
            >
              <XIcon className="size-4" />
            </Button>
          </div>
        </DialogHeader>
        <div className="flex-1 overflow-hidden">
          {conversationId ? (
            <ChatWindow
              conversationId={conversationId}
              messages={activeMessages}
              currentUserId={currentUserId}
              memberName={memberName}
              memberPictureId={memberPictureId}
              onSendMessage={handleSendMessage}
              onMarkAsRead={handleMarkAsRead}
              isConnected={isConnected}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <MessageCircleIcon className="size-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">
                  {isConnected ? "Starting conversation..." : "Connecting..."}
                </p>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
