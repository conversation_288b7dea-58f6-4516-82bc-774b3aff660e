"use client";

import { MessageCircleIcon } from "lucide-react";

import type { ConversationType } from "~/lib/types";
import { ChatDialog } from "./chat-dialog";

interface ChatButtonProps {
  type: ConversationType;
  memberAuthId: string;
  memberName: string;
  memberPictureId: string;
  referenceId?: string;
  variant?: "default" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  children?: React.ReactNode;
}

export function ChatButton({
  type,
  memberAuthId,
  memberName,
  memberPictureId,
  referenceId,
  variant = "outline",
  size = "sm",
  className,
  children,
}: ChatButtonProps) {
  return (
    <ChatDialog
      type={type}
      memberAuthId={memberAuthId}
      memberName={memberName}
      memberPictureId={memberPictureId}
      referenceId={referenceId}
      triggerText=""
      triggerVariant={variant}
      triggerSize={size}
      className={className}
    >
      {children || (
        <>
          <MessageCircleIcon className="size-4" />
          <span className="sr-only">Chat with {memberName}</span>
        </>
      )}
    </ChatDialog>
  );
}

interface ChatButtonWithTextProps extends ChatButtonProps {
  text?: string;
}

export function ChatButtonWithText({
  text = "Chat",
  children,
  ...props
}: ChatButtonWithTextProps) {
  return (
    <ChatButton {...props}>
      {children || (
        <>
          <MessageCircleIcon className="size-4" />
          {text}
        </>
      )}
    </ChatButton>
  );
}
