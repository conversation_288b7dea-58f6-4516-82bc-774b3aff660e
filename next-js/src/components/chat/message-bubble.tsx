"use client";

import { <PERSON><PERSON><PERSON>, CheckCheckIcon } from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import type { PublicMessage } from "~/lib/types";
import { cn, formatDate } from "~/lib/utils";

interface MessageBubbleProps {
  message: PublicMessage;
  isOwnMessage: boolean;
  memberPictureId?: string;
  className?: string;
}

export function MessageBubble({
  message,
  isOwnMessage,
  memberPictureId,
  className,
}: MessageBubbleProps) {
  const sender = message.sender;

  return (
    <div
      className={cn(
        "flex items-start gap-3",
        isOwnMessage ? "flex-row-reverse" : "flex-row",
        className,
      )}
    >
      {/* Avatar */}
      <Avatar className="size-8 flex-shrink-0">
        <AvatarImage
          src={`${process.env.NEXT_PUBLIC_FILE_URL}/${
            isOwnMessage ? sender.pictureId : memberPictureId || sender.pictureId
          }`}
          alt={sender.name}
        />
        <AvatarFallback className="text-xs">
          {sender.name
            .split(" ")
            .map((n) => n[0])
            .join("")
            .toUpperCase()}
        </AvatarFallback>
      </Avatar>

      {/* Message content */}
      <div
        className={cn(
          "flex flex-col max-w-[70%]",
          isOwnMessage ? "items-end" : "items-start",
        )}
      >
        {/* Sender name (only show for other's messages) */}
        {!isOwnMessage && (
          <span className="text-xs text-muted-foreground mb-1">
            {sender.name}
          </span>
        )}

        {/* Message bubble */}
        <div
          className={cn(
            "px-4 py-2 rounded-2xl max-w-full break-words",
            isOwnMessage
              ? "bg-primary text-primary-foreground rounded-br-md"
              : "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-bl-md",
          )}
        >
          <p className="text-sm whitespace-pre-wrap">{message.content}</p>
        </div>

        {/* Message metadata */}
        <div
          className={cn(
            "flex items-center gap-1 mt-1",
            isOwnMessage ? "flex-row-reverse" : "flex-row",
          )}
        >
          <span className="text-xs text-muted-foreground">
            {formatDate(message.createdAt, "HH:mm")}
          </span>

          {/* Read status (only for own messages) */}
          {isOwnMessage && (
            <div className="text-muted-foreground">
              {message.isRead ? (
                <CheckCheckIcon className="size-3" />
              ) : (
                <CheckIcon className="size-3" />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
