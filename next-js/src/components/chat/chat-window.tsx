"use client";

import { useEffect, useRef } from "react";

import type { PublicMessage } from "~/lib/types";
import { cn } from "~/lib/utils";
import { ChatInput } from "./chat-input";
import { MessageBubble } from "./message-bubble";

interface ChatWindowProps {
  messages: PublicMessage[];
  currentUserId: string;
  memberPictureId: string;
  onSendMessage: (content: string) => Promise<void>;
  onMarkAsRead: (messageIds: string[]) => void;
  isConnected: boolean;
  className?: string;
}

export function ChatWindow({
  messages,
  currentUserId,
  memberPictureId,
  onSendMessage,
  onMarkAsRead,
  isConnected,
  className,
}: ChatWindowProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Mark unread messages as read when they come into view
  useEffect(() => {
    const unreadMessages = messages.filter(
      (message) => !message.isRead && message.sender.id !== currentUserId
    );

    if (unreadMessages.length > 0) {
      const messageIds = unreadMessages.map((message) => message.id);
      onMarkAsRead(messageIds);
    }
  }, [messages, currentUserId, onMarkAsRead]);

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Messages area */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900"
      >
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-muted-foreground">
              <p>No messages yet.</p>
              <p className="text-sm">Start the conversation!</p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <MessageBubble
                key={message.id}
                message={message}
                isOwnMessage={message.sender.id === currentUserId}
                memberPictureId={memberPictureId}
              />
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Input area */}
      <ChatInput
        onSendMessage={onSendMessage}
        isDisabled={!isConnected}
        disabledReason={
          !isConnected ? "Connecting..." : "This conversation has ended"
        }
      />
    </div>
  );
}
