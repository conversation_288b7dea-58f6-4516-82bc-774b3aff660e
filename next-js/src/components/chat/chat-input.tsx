"use client";

import { useState } from "react";

import { SendIcon, XCircleIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { cn } from "~/lib/utils";

interface ChatInputProps {
  onSendMessage: (content: string) => Promise<void>;
  isDisabled?: boolean;
  disabledReason?: string;
  placeholder?: string;
  className?: string;
}

export function ChatInput({
  onSendMessage,
  isDisabled = false,
  disabledReason = "This conversation has been ended",
  placeholder = "Type a message...",
  className,
}: ChatInputProps) {
  const [message, setMessage] = useState("");
  const [isSending, setIsSending] = useState(false);

  const handleSend = async () => {
    if (message.trim() && !isDisabled && !isSending) {
      setIsSending(true);
      try {
        await onSendMessage(message.trim());
        setMessage("");
      } catch (error) {
        console.error("Failed to send message:", error);
      } finally {
        setIsSending(false);
      }
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  };

  if (isDisabled) {
    return (
      <div
        className={cn(
          "p-4 border-t border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20",
          className,
        )}
      >
        <div
          className={cn(
            "flex items-center justify-center space-x-2 text-red-600 dark:text-red-400",
          )}
        >
          <XCircleIcon className={cn("size-5")} />
          <span className={cn("text-sm font-medium")}>{disabledReason}</span>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800",
        className,
      )}
    >
      <div className={cn("flex items-center space-x-2")}>
        <Input
          type="text"
          placeholder={placeholder}
          className={cn(
            "flex-1 rounded-full px-4 py-2 border-gray-300 dark:border-gray-600 focus:ring-primary-500 focus:border-primary-500",
          )}
          value={message}
          onChange={(event) => setMessage(event.target.value)}
          onKeyDown={handleKeyDown}
          disabled={isSending}
        />
        <Button
          type="button"
          variant="default"
          size="icon"
          className={cn("rounded-full")}
          disabled={!message.trim() || isSending}
          onClick={handleSend}
        >
          <SendIcon className={cn("size-5")} />
        </Button>
      </div>
    </div>
  );
}
